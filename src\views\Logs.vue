<template>
  <div class="logs-container">
    <div class="card">
      <div class="table-container">
        <div class="table-header">
          <div class="table-title">错误日志列表</div>
          <div class="table-actions">
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
        
        <!-- 搜索表单 -->
        <div class="search-form">
          <el-form :model="searchForm" label-width="80px">
            <!-- 第一行：自动过滤项 -->
            <div class="form-row">
              <el-form-item label="版本号" class="form-item">
                <el-select
                  v-model="searchForm.version"
                  placeholder="请选择版本号"
                  clearable
                  style="width: 200px"
                  @change="handleVersionChange"
                >
                  <el-option
                    v-for="item in versionOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="设备平台" class="form-item">
                <el-select
                  v-model="searchForm.device"
                  placeholder="请选择设备平台"
                  clearable
                  style="width: 200px"
                  @change="handleDeviceChange"
                >
                  <el-option
                    v-for="item in deviceOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="时间范围" class="form-item">
                <DateRangePicker
                  v-model="searchForm.timeRange"
                  @change="handleTimeRangeChange"
                />
              </el-form-item>
            </div>
            
            <!-- 第二行：手动搜索项 -->
            <div class="form-row">
              <el-form-item label="玩家UID" class="form-item">
                <el-input
                  v-model="searchForm.uid"
                  placeholder="请输入玩家UID"
                  clearable
                  style="width: 200px"
                  @keyup.enter="handleSearch"
                />
              </el-form-item>
              <el-form-item label="关键字" class="form-item">
                <el-input
                  v-model="searchForm.keyword"
                  placeholder="请输入错误KEY或错误信息"
                  clearable
                  style="width: 300px"
                  @keyup.enter="handleSearch"
                />
              </el-form-item>
              <el-form-item class="form-item">
                <el-button type="primary" @click="handleSearch">搜索</el-button>
                <el-button @click="resetSearch">重置</el-button>
              </el-form-item>
            </div>
          </el-form>
        </div>

        <!-- 分组控制 -->
        <div class="group-control">
          <el-form inline>
            <el-form-item label="分组方式">
              <el-select
                v-model="groupBy"
                placeholder="请选择分组方式"
                style="width: 200px"
                @change="handleGroupChange"
              >
                <el-option label="无分组" value="none" />
                <el-option label="玩家UID" value="playerUid" />
                <el-option label="错误KEY" value="errorKey" />
                <el-option label="版本号" value="version" />
                <el-option label="设备平台" value="device" />
                <el-option label="错误详情" value="errorMessage" />
              </el-select>
            </el-form-item>
            <el-form-item label="排序">
              <el-select
                v-model="sortOrder"
                style="width: 150px"
                @change="handleSortChange"
              >
                <el-option label="数量从多到少" value="desc" />
                <el-option label="数量从少到多" value="asc" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 分组表格 -->
        <div v-if="groupBy !== 'none'" class="grouped-table">
          <div v-for="group in groupedData" :key="group.groupKey" class="group-section">
            <div class="group-header" @click="toggleGroup(group.groupKey)">
              <el-icon class="expand-icon" :class="{ expanded: group.expanded }">
                <ArrowRight />
              </el-icon>
              <span class="group-title">
                {{ errorLogStore.getGroupFieldLabel(groupBy) }}: {{ group.groupKey }}
              </span>
              <el-tag type="info" class="group-count">{{ group.count }} 条</el-tag>
            </div>
            <div v-if="group.expanded" class="group-content">
              <div v-for="log in group.logs" :key="log.id" class="log-item">
                <div class="log-summary" @click="toggleLogDetail(log.id)">
                  <div class="log-basic-info">
                    <span class="log-id">ID: {{ log.id }}</span>
                    <span class="log-uid">UID: {{ log.playerUid }}</span>
                    <span class="log-version">版本: {{ log.version }}</span>
                    <span class="log-device" v-if="log.device">设备: {{ log.device }}</span>
                    <span class="log-ip">IP: {{ log.ip }}</span>
                    <span class="log-time">{{ log.time }}</span>
                  </div>
                  <div class="log-error-key">{{ log.errorKey }}</div>
                  <el-icon class="expand-icon" :class="{ expanded: errorLogStore.isLogExpanded(log.id) }">
                    <ArrowRight />
                  </el-icon>
                </div>
                <div v-if="errorLogStore.isLogExpanded(log.id)" class="log-detail">
                  <div class="detail-section">
                    <h4>UI路径</h4>
                    <pre class="ui-path">{{ formatUIPath(log.ui) }}</pre>
                  </div>
                  <div class="detail-section">
                    <div class="error-header">
                      <h4>错误详情</h4>
                      <div class="error-actions">
                        <el-button 
                          type="info"
                          size="small"
                          @click.stop="handleCopy(log.restoredErrorMessage || log.errorMessage)"
                        >
                          复制
                        </el-button>
                        <el-button 
                          v-if="!log.isRestored && log.device"
                          type="primary" 
                          size="small"
                          @click="handleSingleRestore(log.id)"
                          :loading="errorLogStore.isLogRestoring(log.id)"
                        >
                          还原堆栈
                        </el-button>
                        <el-tag 
                          v-if="log.isRestored" 
                          type="success" 
                          size="small"
                        >
                          已还原
                        </el-tag>
                      </div>
                    </div>
                    <pre class="error-message">{{ formatErrorMessage(log.restoredErrorMessage || log.errorMessage) }}</pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 普通列表 -->
        <div v-else class="normal-list">
          <div v-for="log in paginatedErrorLogs" :key="log.id" class="log-item">
            <div class="log-summary" @click="toggleLogDetail(log.id)">
              <div class="log-basic-info">
                <span class="log-id">ID: {{ log.id }}</span>
                <span class="log-uid">UID: {{ log.playerUid }}</span>
                <span class="log-version">版本: {{ log.version }}</span>
                <span class="log-device" v-if="log.device">设备: {{ log.device }}</span>
                <span class="log-ip">IP: {{ log.ip }}</span>
                <span class="log-time">{{ log.time }}</span>
              </div>
              <div class="log-error-key">{{ log.errorKey }}</div>
              <el-icon class="expand-icon" :class="{ expanded: errorLogStore.isLogExpanded(log.id) }">
                <ArrowRight />
              </el-icon>
            </div>
            <div v-if="errorLogStore.isLogExpanded(log.id)" class="log-detail">
              <div class="detail-section">
                <h4>UI路径</h4>
                <pre class="ui-path">{{ formatUIPath(log.ui) }}</pre>
              </div>
              <div class="detail-section">
                <div class="error-header">
                  <h4>错误详情</h4>
                  <div class="error-actions">
                    <el-button 
                      type="info"
                      size="small"
                      @click.stop="handleCopy(log.restoredErrorMessage || log.errorMessage)"
                    >
                      复制
                    </el-button>
                    <el-button 
                      v-if="!log.isRestored && log.device"
                      type="primary" 
                      size="small"
                      @click="handleSingleRestore(log.id)"
                      :loading="errorLogStore.isLogRestoring(log.id)"
                    >
                      还原堆栈
                    </el-button>
                    <el-tag 
                      v-if="log.isRestored" 
                      type="success" 
                      size="small"
                    >
                      已还原
                    </el-tag>
                  </div>
                </div>
                <pre class="error-message">{{ formatErrorMessage(log.restoredErrorMessage || log.errorMessage) }}</pre>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 分页 -->
        <div v-if="groupBy === 'none'" class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, onMounted, ref } from 'vue'
import { useErrorLogStore } from '@/stores/errorLog'
import type { SearchFilterParams, GroupByOption } from '@/types'
import { ElMessage } from 'element-plus'
import DateRangePicker from '@/components/DateRangePicker.vue'

const errorLogStore = useErrorLogStore()


// 格式化错误堆栈信息
const formatErrorMessage = (errorMessage: string) => {
  if (!errorMessage) return ''
  // 检测是否为Unity堆栈跟踪格式
  if (errorMessage.includes('(at :0)') || errorMessage.includes('(at ')) {
    // 只在方法调用结束后添加换行，保持(at :0)在同一行
    return errorMessage.replace(/\) ([A-Za-z$])/g, ')\n$1')
  }
  
  // 其他格式的错误信息保持原样
  return errorMessage
}

// 复制错误信息
const handleCopy = (text: string) => {
  if (!text) {
    ElMessage.warning('没有可复制的内容')
    return
  }
  const formattedText = formatErrorMessage(text)
  navigator.clipboard.writeText(formattedText).then(() => {
    ElMessage.success('复制成功')
  }, () => {
    ElMessage.error('复制失败，请手动复制')
  })
}

// 格式化UI路径 - 保持原样不添加换行
const formatUIPath = (uiPath: string) => {
  return uiPath
}

// 搜索表单
const searchForm = reactive({
  uid: '',
  keyword: '',
  version: '',
  device: '',
  timeRange: null as [string, string] | null
})


// 分页信息
const pagination = computed(() => errorLogStore.pagination)


// 分页后的错误日志
const paginatedErrorLogs = computed(() => errorLogStore.paginatedErrorLogs)

// 版本选项
const versionOptions = computed(() => errorLogStore.versionOptions)

// 设备选项
const deviceOptions = computed(() => errorLogStore.deviceOptions)


// 分组相关
const groupBy = ref<GroupByOption>('errorMessage')
const sortOrder = ref<'asc' | 'desc'>('desc')
const groupedData = computed(() => errorLogStore.groupedErrorLogs)

// 刷新数据
const refreshData = () => {
  errorLogStore.fetchErrorLogs()
}

// 处理时间范围变化
const handleTimeRangeChange = (value: [string, string] | null) => {
  searchForm.timeRange = value
  handleAutoSearch()
}

// 处理版本号变化
const handleVersionChange = (value: string) => {
  searchForm.version = value
  handleAutoSearch()
}

// 处理设备变化
const handleDeviceChange = (value: string) => {
  searchForm.device = value
  handleAutoSearch()
}

// 自动搜索处理（版本号、设备、时间范围变化时）
const handleAutoSearch = () => {
  const filter: SearchFilterParams = {
    uid: searchForm.uid || undefined,
    keyword: searchForm.keyword || undefined,
    version: searchForm.version || undefined,
    device: searchForm.device || undefined,
    startTime: searchForm.timeRange?.[0],
    endTime: searchForm.timeRange?.[1]
  }
  errorLogStore.updateSearchFilter(filter)
}

// 手动搜索处理（UID、关键字搜索）
const handleSearch = () => {
  const filter: SearchFilterParams = {
    uid: searchForm.uid || undefined,
    keyword: searchForm.keyword || undefined,
    version: searchForm.version || undefined,
    device: searchForm.device || undefined,
    startTime: searchForm.timeRange?.[0],
    endTime: searchForm.timeRange?.[1]
  }
  errorLogStore.updateSearchFilter(filter)
}

// 重置搜索
const resetSearch = () => {
  searchForm.uid = ''
  searchForm.keyword = ''
  searchForm.version = ''
  searchForm.device = ''
  searchForm.timeRange = null
  handleSearch()
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  errorLogStore.updatePagination(pagination.value.page, size)
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  errorLogStore.updatePagination(page)
}

// 切换日志详情展开
const toggleLogDetail = (id: number) => {
  errorLogStore.toggleLogExpansion(id)
}

// 分组变化处理
const handleGroupChange = (value: GroupByOption) => {
  errorLogStore.updateGroupParams({ groupBy: value })
}

// 排序变化处理  
const handleSortChange = (value: 'asc' | 'desc') => {
  errorLogStore.updateGroupParams({ sortOrder: value })
}

// 切换分组展开状态
const toggleGroup = (groupKey: string) => {
  errorLogStore.toggleGroupExpansion(groupKey)
}

// 单个日志堆栈还原
const handleSingleRestore = async (logId: number) => {
  try {
    console.log(`开始还原日志 ${logId} 的堆栈`)
    const success = await errorLogStore.restoreLogStackTrace(logId)
    if (success) {
      ElMessage.success('堆栈还原成功')
    } else {
      ElMessage.error('堆栈还原失败，请查看浏览器控制台了解详细信息')
    }
  } catch (error) {
    console.error('堆栈还原错误:', error)
    ElMessage.error('堆栈还原失败，请查看浏览器控制台了解详细信息')
  }
}



onMounted(() => {
  errorLogStore.fetchErrorLogs()
})
</script>

<style scoped lang="scss">
.logs-container {
  .search-form {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    
    .form-row {
      display: flex;
      align-items: center;
      gap: 20px;
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .form-item {
      margin-right: 0;
      margin-bottom: 0;
    }
    
    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #333;
    }
  }

  .group-control {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f0f2f5;
    border-radius: 4px;
  }

  .grouped-table, .normal-list {
    .group-section {
      margin-bottom: 20px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      overflow: hidden;

      .group-header {
        padding: 12px 16px;
        background-color: #f5f7fa;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        border-bottom: 1px solid #e0e0e0;
        transition: background-color 0.2s;

        &:hover {
          background-color: #e6f7ff;
        }

        .expand-icon {
          transition: transform 0.2s;
          &.expanded {
            transform: rotate(90deg);
          }
        }

        .group-title {
          font-weight: 500;
          flex: 1;
        }

        .group-count {
          margin-left: auto;
        }
      }

      .group-content {
        padding: 16px;
        background-color: #fff;
      }
    }

    .log-item {
      margin-bottom: 12px;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      overflow: hidden;

      .log-summary {
        padding: 12px 16px;
        background-color: #fafafa;
        cursor: pointer;
        position: relative;
        transition: background-color 0.2s;

        &:hover {
          background-color: #f0f9ff;
        }

        .log-basic-info {
          display: flex;
          gap: 16px;
          margin-bottom: 8px;
          font-size: 12px;
          color: #666;

          span {
            padding: 2px 8px;
            background-color: #f0f0f0;
            border-radius: 3px;
          }

          .log-id { background-color: #e3f2fd; color: #1976d2; }
          .log-uid { background-color: #f3e5f5; color: #7b1fa2; }
          .log-version { background-color: #e8f5e8; color: #388e3c; }
          .log-device { background-color: #f1f8e9; color: #689f38; }
          .log-ip { background-color: #fff3e0; color: #f57c00; }
        }

        .log-error-key {
          font-weight: 500;
          color: #333;
          padding-right: 30px;
        }

        .expand-icon {
          position: absolute;
          right: 16px;
          top: 50%;
          transform: translateY(-50%);
          transition: transform 0.2s;
          color: #666;

          &.expanded {
            transform: translateY(-50%) rotate(90deg);
          }
        }
      }

      .log-detail {
        padding: 16px;
        background-color: #fff;
        border-top: 1px solid #e8e8e8;

        .detail-section {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .error-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            h4 {
              margin: 0;
              font-size: 14px;
              font-weight: 600;
              color: #333;
              margin-right: 12px;
            }

            .error-actions {
              display: flex;
              gap: 8px;
              align-items: center;
            }
          }

          h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
            color: #333;
          }

          .ui-path {
            padding: 12px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            color: #495057;
            white-space: pre-wrap;
            word-break: break-all;
            overflow-x: auto;
            margin: 0;
          }

          .error-message {
            padding: 12px;
            background-color: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.6;
            color: #e53e3e;
            white-space: pre;
            overflow: auto;
            max-height: 300px;
            margin: 0;
            
            // 为堆栈跟踪添加语法高亮
            :deep() {
              // 高亮方法名
              .method {
                color: #0066cc;
                font-weight: 500;
              }
              
              // 高亮文件位置信息
              .location {
                color: #666;
                font-style: italic;
              }
            }
          }
        }
      }
    }
  }
}
</style>